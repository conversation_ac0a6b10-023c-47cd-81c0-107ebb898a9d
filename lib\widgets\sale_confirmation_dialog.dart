import 'package:flutter/material.dart';
import '../utils/dialog_theme.dart' as custom_dialog;
import '../utils/app_colors.dart';
import '../utils/currency_utils.dart';

/// 판매 확인 다이얼로그 - 세련된 디자인
class SaleConfirmationDialog extends StatelessWidget {
  final List<SaleItem> items;
  final int totalAmount;
  final String? setDiscountInfo;
  final VoidCallback? onConfirm;
  final VoidCallback? onCancel;

  const SaleConfirmationDialog({
    super.key,
    required this.items,
    required this.totalAmount,
    this.setDiscountInfo,
    this.onConfirm,
    this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    return custom_dialog.DialogTheme.buildModernDialog(
      isCompact: false, // 더 넓은 다이얼로그 사용
      child: Padding(
        padding: EdgeInsets.all(isTablet ? 24.0 : 20.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 제목 섹션
            _buildHeader(context, isTablet),
            
            SizedBox(height: isTablet ? 24.0 : 20.0),
            
            // 상품 목록 섹션
            _buildProductList(context, isTablet),
            
            SizedBox(height: isTablet ? 20.0 : 16.0),
            
            // 세트 할인 정보 (있는 경우)
            if (setDiscountInfo != null && setDiscountInfo!.isNotEmpty)
              _buildDiscountInfo(context, isTablet),
            
            // 총 금액 섹션
            _buildTotalSection(context, isTablet),
            
            SizedBox(height: isTablet ? 24.0 : 20.0),
            
            // 버튼 섹션
            _buildButtons(context, isTablet),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, bool isTablet) {
    return Row(
      children: [
        Container(
          width: isTablet ? 48.0 : 40.0,
          height: isTablet ? 48.0 : 40.0,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                OnboardingColors.primary.withValues(alpha: 0.2),
                OnboardingColors.primaryLight.withValues(alpha: 0.1),
              ],
            ),
            borderRadius: BorderRadius.circular(isTablet ? 12.0 : 10.0),
          ),
          child: Icon(
            Icons.shopping_cart_checkout,
            color: OnboardingColors.primary,
            size: isTablet ? 24.0 : 20.0,
          ),
        ),
        SizedBox(width: isTablet ? 16.0 : 12.0),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'POS 판매 확인',
                style: TextStyle(
                  fontSize: isTablet ? 20.0 : 18.0,
                  fontWeight: FontWeight.bold,
                  color: OnboardingColors.textPrimary,
                  fontFamily: 'Pretendard',
                ),
              ),
              SizedBox(height: 4.0),
              Text(
                '선택하신 상품을 확인해주세요',
                style: TextStyle(
                  fontSize: isTablet ? 14.0 : 12.0,
                  color: OnboardingColors.textSecondary,
                  fontFamily: 'Pretendard',
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildProductList(BuildContext context, bool isTablet) {
    return Container(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.3,
      ),
      decoration: BoxDecoration(
        color: OnboardingColors.surface.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(isTablet ? 12.0 : 10.0),
        border: Border.all(
          color: OnboardingColors.primary.withValues(alpha: 0.1),
          width: 1.0,
        ),
      ),
      child: SingleChildScrollView(
        padding: EdgeInsets.all(isTablet ? 16.0 : 12.0),
        child: Column(
          children: [
            for (int i = 0; i < items.length; i++) ...[
              _buildProductItem(context, items[i], isTablet),
              if (i < items.length - 1) // 마지막 아이템이 아니면 구분선 추가
                Padding(
                  padding: EdgeInsets.symmetric(vertical: isTablet ? 8.0 : 6.0),
                  child: Divider(
                    color: OnboardingColors.primary.withValues(alpha: 0.1),
                    thickness: 0.5,
                    height: 1,
                  ),
                ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildProductItem(BuildContext context, SaleItem item, bool isTablet) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: isTablet ? 4.0 : 3.0),
      child: Row(
        children: [
          // 상품 정보
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.displayName,
                  style: TextStyle(
                    fontSize: isTablet ? 14.0 : 13.0,
                    fontWeight: FontWeight.w600,
                    color: OnboardingColors.textPrimary,
                    fontFamily: 'Pretendard',
                  ),
                ),
                SizedBox(height: 2.0),
                Text(
                  '${CurrencyUtils.formatCurrency(item.unitPrice)}원',
                  style: TextStyle(
                    fontSize: isTablet ? 12.0 : 11.0,
                    color: OnboardingColors.textSecondary,
                    fontFamily: 'Pretendard',
                  ),
                ),
              ],
            ),
          ),

          // 수량
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: isTablet ? 8.0 : 6.0,
              vertical: isTablet ? 4.0 : 3.0,
            ),
            decoration: BoxDecoration(
              color: OnboardingColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(isTablet ? 6.0 : 5.0),
            ),
            child: Text(
              'x${item.quantity}',
              style: TextStyle(
                fontSize: isTablet ? 12.0 : 11.0,
                fontWeight: FontWeight.w600,
                color: OnboardingColors.primary,
                fontFamily: 'Pretendard',
              ),
            ),
          ),

          SizedBox(width: isTablet ? 12.0 : 8.0),

          // 소계
          Text(
            '${CurrencyUtils.formatCurrency(item.subtotal)}원',
            style: TextStyle(
              fontSize: isTablet ? 14.0 : 13.0,
              fontWeight: FontWeight.bold,
              color: OnboardingColors.accent,
              fontFamily: 'Pretendard',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDiscountInfo(BuildContext context, bool isTablet) {
    return Container(
      margin: EdgeInsets.only(bottom: isTablet ? 16.0 : 12.0),
      padding: EdgeInsets.all(isTablet ? 16.0 : 12.0),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            OnboardingColors.accent.withValues(alpha: 0.1),
            OnboardingColors.accentLight.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(isTablet ? 12.0 : 10.0),
        border: Border.all(
          color: OnboardingColors.accent.withValues(alpha: 0.2),
          width: 1.0,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.local_offer,
            color: OnboardingColors.accent,
            size: isTablet ? 20.0 : 18.0,
          ),
          SizedBox(width: isTablet ? 12.0 : 8.0),
          Expanded(
            child: Text(
              setDiscountInfo!,
              style: TextStyle(
                fontSize: isTablet ? 13.0 : 12.0,
                color: OnboardingColors.accent,
                fontFamily: 'Pretendard',
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTotalSection(BuildContext context, bool isTablet) {
    return Container(
      padding: EdgeInsets.all(isTablet ? 20.0 : 16.0),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            OnboardingColors.primary.withValues(alpha: 0.1),
            OnboardingColors.primaryLight.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(isTablet ? 16.0 : 12.0),
        border: Border.all(
          color: OnboardingColors.primary.withValues(alpha: 0.2),
          width: 1.5,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '총 결제 금액',
            style: TextStyle(
              fontSize: isTablet ? 18.0 : 16.0,
              fontWeight: FontWeight.w600,
              color: OnboardingColors.textPrimary,
              fontFamily: 'Pretendard',
            ),
          ),
          Text(
            '${CurrencyUtils.formatCurrency(totalAmount)}원',
            style: TextStyle(
              fontSize: isTablet ? 22.0 : 20.0,
              fontWeight: FontWeight.bold,
              color: OnboardingColors.primary,
              fontFamily: 'Pretendard',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildButtons(BuildContext context, bool isTablet) {
    return Row(
      children: [
        // 취소 버튼
        Expanded(
          child: custom_dialog.DialogTheme.buildModernButton(
            text: '취소',
            onPressed: () {
              Navigator.of(context).pop(false);
              onCancel?.call();
            },
            isTablet: isTablet,
            isPrimary: false,
          ),
        ),
        SizedBox(width: isTablet ? 16.0 : 12.0),
        
        // 판매 버튼
        Expanded(
          child: custom_dialog.DialogTheme.buildModernButton(
            text: '판매 확정',
            onPressed: () {
              Navigator.of(context).pop(true);
              onConfirm?.call();
            },
            isTablet: isTablet,
            isPrimary: true,
          ),
        ),
      ],
    );
  }

  /// 판매 확인 다이얼로그 표시
  static Future<bool?> show({
    required BuildContext context,
    required List<SaleItem> items,
    required int totalAmount,
    String? setDiscountInfo,
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
  }) {
    return showDialog<bool>(
      context: context,
      builder: (context) => SaleConfirmationDialog(
        items: items,
        totalAmount: totalAmount,
        setDiscountInfo: setDiscountInfo,
        onConfirm: onConfirm,
        onCancel: onCancel,
      ),
    );
  }
}

/// 판매 아이템 정보
class SaleItem {
  final String displayName;
  final int quantity;
  final int unitPrice;
  final int subtotal;

  const SaleItem({
    required this.displayName,
    required this.quantity,
    required this.unitPrice,
    required this.subtotal,
  });
}
