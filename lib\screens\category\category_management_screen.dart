import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/category.dart';
import '../../providers/category_provider.dart';
import '../../providers/unified_workspace_provider.dart';
import '../../widgets/confirmation_dialog.dart';
import '../../constants/category_colors.dart';

/// 카테고리 관리 화면
/// 드래그 앤 드롭으로 순서 변경 가능
class CategoryManagementScreen extends ConsumerStatefulWidget {
  const CategoryManagementScreen({super.key});

  @override
  ConsumerState<CategoryManagementScreen> createState() => _CategoryManagementScreenState();
}

class _CategoryManagementScreenState extends ConsumerState<CategoryManagementScreen> {
  final TextEditingController _categoryNameController = TextEditingController();
  List<Category> _reorderableCategories = [];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadCategories();
    });
  }

  @override
  void dispose() {
    _categoryNameController.dispose();
    super.dispose();
  }

  /// 카테고리 목록 로드
  void _loadCategories() {
    final currentWorkspace = ref.read(currentWorkspaceProvider);
    if (currentWorkspace != null) {
      ref.read(categoryNotifierProvider.notifier).loadCategories(eventId: currentWorkspace.id);
    }
  }

  /// 새 카테고리 추가 다이얼로그
  void _showAddCategoryDialog() async {
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => const _AddCategoryDialog(),
    );

    if (result != null && result['name'] != null && result['name'].isNotEmpty) {
      await _addCategoryWithNameAndColor(result['name'], result['color']);
    }
  }

  /// 카테고리 수정 다이얼로그
  void _showEditCategoryDialog(Category category) async {
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => _EditCategoryDialog(category: category),
    );

    if (result != null && result['name'] != null && result['name'].isNotEmpty) {
      await _updateCategoryWithNameAndColor(category.id!, result['name'], result['color']);
    }
  }

  /// 카테고리 삭제 확인 다이얼로그
  void _showDeleteConfirmDialog(Category category) {
    ConfirmationDialog.showDelete(
      context: context,
      title: '카테고리 삭제',
      message: '"${category.name}" 카테고리를 삭제하시겠습니까?\n\n카테고리에 상품이 있으면 삭제할 수 없습니다.',
      confirmLabel: '삭제',
      cancelLabel: '취소',
      onConfirm: () {
        _deleteCategory(category.id!);
      },
    );
  }

  /// 카테고리 추가 (색상 포함)
  Future<void> _addCategoryWithNameAndColor(String name, int? color) async {
    // 현재 워크스페이스 ID 가져오기
    final currentWorkspace = ref.read(currentWorkspaceProvider);
    if (currentWorkspace == null) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('현재 워크스페이스가 선택되지 않았습니다.'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    final success = await ref.read(categoryNotifierProvider.notifier).addCategory(
      name: name,
      eventId: currentWorkspace.id,
      color: color,
    );

    if (!mounted) return;

    if (success) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('카테고리가 추가되었습니다.')),
      );
    } else {
      final error = ref.read(categoryNotifierProvider.notifier).errorMessage;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(error ?? '카테고리 추가에 실패했습니다.'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// 카테고리 수정 (색상 포함)
  Future<void> _updateCategoryWithNameAndColor(int id, String name, int? color) async {
    final success = await ref.read(categoryNotifierProvider.notifier).updateCategory(
      id: id,
      name: name,
      color: color,
    );

    if (!mounted) return;

    if (success) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('카테고리가 수정되었습니다.')),
      );
    } else {
      final error = ref.read(categoryNotifierProvider.notifier).errorMessage;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(error ?? '카테고리 수정에 실패했습니다.'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// 카테고리 삭제
  Future<void> _deleteCategory(int id) async {
    final success = await ref.read(categoryNotifierProvider.notifier).deleteCategory(id);
    
    if (success) {
      _showSnackBar('카테고리가 삭제되었습니다.');
    } else {
      final error = ref.read(categoryNotifierProvider.notifier).errorMessage;
      _showSnackBar(error ?? '카테고리 삭제에 실패했습니다.');
    }
  }

  /// 카테고리 순서 재정렬
  Future<void> _reorderCategories(int oldIndex, int newIndex) async {
    setState(() {
      if (newIndex > oldIndex) {
        newIndex -= 1;
      }
      final Category item = _reorderableCategories.removeAt(oldIndex);
      _reorderableCategories.insert(newIndex, item);
    });

    final success = await ref.read(categoryNotifierProvider.notifier).reorderCategories(_reorderableCategories);
    
    if (!success) {
      _showSnackBar('카테고리 순서 변경에 실패했습니다.');
      // 실패시 원래 순서로 되돌리기
      _loadCategories();
    }
  }

  /// 스낵바 표시
  void _showSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(message)),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final categoriesAsync = ref.watch(categoryNotifierProvider);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('카테고리 관리'),
      ),
      body: categoriesAsync.when(
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, _) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              Text(
                '카테고리 로드 중 오류가 발생했습니다.\n$error',
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontFamily: 'Pretendard', fontSize: 16),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _loadCategories,
                child: const Text('다시 시도'),
              ),
            ],
          ),
        ),
        data: (categories) {
          _reorderableCategories = List.from(categories);
          
          if (categories.isEmpty) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.category,
                    size: 64,
                    color: Colors.grey,
                  ),
                  SizedBox(height: 16),
                  Text(
                    '등록된 카테고리가 없습니다.\n아래 버튼을 눌러 새 카테고리를 추가해보세요.',
                    textAlign: TextAlign.center,
                    style: TextStyle(fontSize: 16, color: Colors.grey),
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              // 카테고리 목록
              Expanded(
                child: ReorderableListView.builder(
                  itemCount: categories.length,
                  onReorder: _reorderCategories,
                  buildDefaultDragHandles: false, // 기본 드래그 핸들 비활성화
                  itemBuilder: (context, index) {
                    final category = categories[index];
                    return Card(
                      key: ValueKey(category.id),
                      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                      child: ListTile(
                        leading: const Icon(Icons.category),
                        title: Text(
                          category.name,
                          style: TextStyle(fontFamily: 'Pretendard', 
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        subtitle: Text('순서: ${category.sortOrder + 1}'),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            IconButton(
                              icon: const Icon(Icons.edit),
                              onPressed: () => _showEditCategoryDialog(category),
                              tooltip: '수정',
                            ),
                            IconButton(
                              icon: const Icon(Icons.delete, color: Colors.red),
                              onPressed: () => _showDeleteConfirmDialog(category),
                              tooltip: '삭제',
                            ),
                            ReorderableDragStartListener(
                              index: index,
                              child: const Icon(
                                Icons.drag_handle,
                                color: Colors.grey,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddCategoryDialog,
        tooltip: '새 카테고리 추가',
        child: const Icon(Icons.add),
      ),
    );
  }
}

/// 카테고리 추가 다이얼로그 (색상 선택 포함)
class _AddCategoryDialog extends StatefulWidget {
  const _AddCategoryDialog();

  @override
  State<_AddCategoryDialog> createState() => _AddCategoryDialogState();
}

class _AddCategoryDialogState extends State<_AddCategoryDialog> {
  final _controller = TextEditingController();
  int _selectedColor = CategoryColors.defaultColorValue;
  String? _error;

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _submit() {
    final name = _controller.text.trim();
    if (name.isEmpty) {
      setState(() => _error = '카테고리 이름을 입력해주세요.');
      return;
    }

    Navigator.of(context).pop({
      'name': name,
      'color': _selectedColor,
    });
  }

  Widget _buildColorSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '색상 선택',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 12),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 5,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 1,
          ),
          itemCount: CategoryColors.availableColors.length,
          itemBuilder: (context, index) {
            final color = CategoryColors.availableColors[index];
            final isSelected = color.toARGB32() == _selectedColor;

            return GestureDetector(
              onTap: () => setState(() => _selectedColor = color.toARGB32()),
              child: Container(
                decoration: BoxDecoration(
                  color: color,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: isSelected ? Colors.black87 : Colors.grey.shade300,
                    width: isSelected ? 3 : 1,
                  ),
                  boxShadow: isSelected ? [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.2),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ] : null,
                ),
                child: isSelected
                  ? const Icon(
                      Icons.check,
                      color: Colors.black87,
                      size: 20,
                    )
                  : null,
              ),
            );
          },
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('새 카테고리 추가'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextField(
              controller: _controller,
              decoration: InputDecoration(
                hintText: '카테고리 이름을 입력하세요',
                errorText: _error,
                border: const OutlineInputBorder(),
              ),
              autofocus: true,
              onSubmitted: (_) => _submit(),
            ),
            const SizedBox(height: 24),
            _buildColorSelector(),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('취소'),
        ),
        ElevatedButton(
          onPressed: _submit,
          child: const Text('추가'),
        ),
      ],
    );
  }
}

/// 카테고리 수정 다이얼로그 (색상 선택 포함)
class _EditCategoryDialog extends StatefulWidget {
  final Category category;

  const _EditCategoryDialog({required this.category});

  @override
  State<_EditCategoryDialog> createState() => _EditCategoryDialogState();
}

class _EditCategoryDialogState extends State<_EditCategoryDialog> {
  late final TextEditingController _controller;
  late int _selectedColor;
  String? _error;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.category.name);
    _selectedColor = widget.category.color;
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _submit() {
    final name = _controller.text.trim();
    if (name.isEmpty) {
      setState(() => _error = '카테고리 이름을 입력해주세요.');
      return;
    }

    Navigator.of(context).pop({
      'name': name,
      'color': _selectedColor,
    });
  }

  Widget _buildColorSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '색상 선택',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 12),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 5,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 1,
          ),
          itemCount: CategoryColors.availableColors.length,
          itemBuilder: (context, index) {
            final color = CategoryColors.availableColors[index];
            final isSelected = color.toARGB32() == _selectedColor;

            return GestureDetector(
              onTap: () => setState(() => _selectedColor = color.toARGB32()),
              child: Container(
                decoration: BoxDecoration(
                  color: color,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: isSelected ? Colors.black87 : Colors.grey.shade300,
                    width: isSelected ? 3 : 1,
                  ),
                  boxShadow: isSelected ? [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.2),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ] : null,
                ),
                child: isSelected
                  ? const Icon(
                      Icons.check,
                      color: Colors.black87,
                      size: 20,
                    )
                  : null,
              ),
            );
          },
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('카테고리 수정'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextField(
              controller: _controller,
              decoration: InputDecoration(
                hintText: '카테고리 이름을 입력하세요',
                errorText: _error,
                border: const OutlineInputBorder(),
              ),
              autofocus: true,
              onSubmitted: (_) => _submit(),
            ),
            const SizedBox(height: 24),
            _buildColorSelector(),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('취소'),
        ),
        ElevatedButton(
          onPressed: _submit,
          child: const Text('수정'),
        ),
      ],
    );
  }
}


