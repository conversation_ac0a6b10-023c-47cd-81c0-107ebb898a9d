/// Blue Booth Manager - 카테고리 색상 상수
///
/// 카테고리에 사용할 수 있는 파스텔 색상들을 정의합니다.
/// - 10가지 파스텔 색상 제공
/// - 기본 색상 포함
/// - 색상 선택 UI에서 사용
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 8월
library;

import 'package:flutter/material.dart';

/// 카테고리에 사용할 수 있는 색상들을 정의하는 클래스
class CategoryColors {
  /// 기본 회색 (기존 기본값)
  static const Color defaultGray = Color(0xFFE0E0E0);
  
  /// 파스텔 핑크
  static const Color pastelPink = Color(0xFFFFD1DC);
  
  /// 파스텔 블루
  static const Color pastelBlue = Color(0xFFADD8E6);
  
  /// 파스텔 그린
  static const Color pastelGreen = Color(0xFF98FB98);
  
  /// 파스텔 옐로우
  static const Color pastelYellow = Color(0xFFFFFFE0);
  
  /// 파스텔 퍼플
  static const Color pastelPurple = Color(0xFFDDA0DD);
  
  /// 파스텔 오렌지
  static const Color pastelOrange = Color(0xFFFFDAB9);
  
  /// 파스텔 민트
  static const Color pastelMint = Color(0xFFAFEEEE);
  
  /// 파스텔 라벤더
  static const Color pastelLavender = Color(0xFFE6E6FA);
  
  /// 파스텔 피치
  static const Color pastelPeach = Color(0xFFFFE5B4);

  /// 사용 가능한 모든 색상 목록
  static const List<Color> availableColors = [
    defaultGray,
    pastelPink,
    pastelBlue,
    pastelGreen,
    pastelYellow,
    pastelPurple,
    pastelOrange,
    pastelMint,
    pastelLavender,
    pastelPeach,
  ];

  /// 색상 이름 목록 (UI 표시용)
  static const List<String> colorNames = [
    '기본 회색',
    '파스텔 핑크',
    '파스텔 블루',
    '파스텔 그린',
    '파스텔 옐로우',
    '파스텔 퍼플',
    '파스텔 오렌지',
    '파스텔 민트',
    '파스텔 라벤더',
    '파스텔 피치',
  ];

  /// 기본 색상 값 (int)
  static const int defaultColorValue = 0xFFE0E0E0;

  /// 색상 값으로 Color 객체 생성
  static Color fromValue(int colorValue) {
    return Color(colorValue);
  }

  /// Color 객체에서 색상 값 추출
  static int toValue(Color color) {
    return color.toARGB32();
  }

  /// 색상이 유효한 카테고리 색상인지 확인
  static bool isValidCategoryColor(int colorValue) {
    return availableColors.any((color) => color.toARGB32() == colorValue);
  }

  /// 색상 값으로 색상 이름 가져오기
  static String getColorName(int colorValue) {
    final index = availableColors.indexWhere((color) => color.toARGB32() == colorValue);
    if (index >= 0 && index < colorNames.length) {
      return colorNames[index];
    }
    return '알 수 없는 색상';
  }
}
